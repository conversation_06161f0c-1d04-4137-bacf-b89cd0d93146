"""
持仓分析模块
负责持仓集中度分析和行业持仓市值贡献比例分析
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class PositionAnalyzer:
    """持仓分析器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化持仓分析器

        Args:
            config: 分析配置字典
        """
        self.config = config.get('position_analysis', {})
        self.concentration_fields = self.config.get('concentration_fields', ['symbol_category', 'industry'])
        self.strategy_categories = self.config.get('strategy_categories', [])
        # 注意：删除集中度等级相关配置

    def analyze_positions(self, cta_data: pd.DataFrame, analysis_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        执行持仓分析模块的所有功能，支持指定日期分析。

        Args:
            cta_data (pd.DataFrame): 预处理后的CTA每日盈亏数据（包含持仓市值）。
            analysis_date (datetime, optional): 指定分析的日期。如果为None，则分析cta_data中的最新一日数据。

        Returns:
            dict: 包含所有持仓分析结果的字典。
        """
        try:
            # 2.1 根据传入的日期筛选数据
            if analysis_date is None:
                # 默认分析最新一日的数据
                latest_date = cta_data['trade_date'].max()
                filtered_df = cta_data[cta_data['trade_date'] == latest_date].copy()
                logger.info(f"分析最新一日数据: {latest_date}")
            else:
                # 根据传入的日期筛选数据
                filtered_df = cta_data[cta_data['trade_date'] == analysis_date].copy()
                logger.info(f"分析指定日期数据: {analysis_date}")

            if filtered_df.empty:
                logger.warning(f"指定日期 {analysis_date or '最新一日'} 无持仓数据可供分析。")
                return {}  # 返回空结果

            logger.info(f"筛选后数据量: {len(filtered_df)} 条记录")

            results = {}

            # 1. 持仓集中度分析（删除集中度等级，增加单位转换和比例调整）
            results['concentration_analysis'] = self._concentration_analysis(filtered_df)

            # 2. 行业持仓市值贡献比例分析
            results['industry_contribution_analysis'] = self._industry_contribution_analysis(filtered_df)

            # 3. 持仓分布统计
            results['position_distribution'] = self._position_distribution_analysis(filtered_df)

            # 4. 风险敞口分析
            results['risk_exposure_analysis'] = self._risk_exposure_analysis(filtered_df)

            logger.info("持仓分析完成")
            return results

        except Exception as e:
            logger.error(f"持仓分析失败: {str(e)}")
            raise
    
    def _concentration_analysis(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        持仓集中度分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            集中度分析结果
        """
        concentration_results = {}
        
        # 所有持仓的集中度分析
        concentration_results['overall'] = self._calculate_concentration(cta_data, "全部持仓")
        
        # 按策略类别分组的集中度分析
        if 'strategy_category' in cta_data.columns:
            strategy_categories = cta_data['strategy_category'].unique()
            
            for strategy in strategy_categories:
                strategy_data = cta_data[cta_data['strategy_category'] == strategy]
                concentration_results[f'strategy_{strategy}'] = self._calculate_concentration(
                    strategy_data, f"策略_{strategy}"
                )
        
        return concentration_results
    
    def _calculate_concentration(self, data: pd.DataFrame, category_name: str) -> pd.DataFrame:
        """
        计算特定数据集的集中度

        Args:
            data: 数据集
            category_name: 类别名称

        Returns:
            集中度分析结果DataFrame
        """
        concentration_data = []

        for field in self.concentration_fields:
            if field not in data.columns:
                continue

            # 按字段透视持仓市值
            if 'position_amount' in data.columns:
                position_summary = data.groupby(field)['position_amount'].sum().abs()
            else:
                # 如果没有position_amount字段，使用profit_loss_amount的绝对值作为代理
                position_summary = data.groupby(field)['profit_loss_amount'].sum().abs()

            total_position = position_summary.sum()

            if total_position > 0:
                concentration_ratios = position_summary / total_position

                for category, position_value in position_summary.items():
                    concentration_ratio = concentration_ratios[category]

                    # 2.3 单位转换和比例调整
                    position_value_wan = position_value / 10000  # 转换为万元
                    concentration_percentage = concentration_ratio * 100  # 转换为百分比

                    concentration_data.append({
                        '分析类别': category_name,
                        '透视字段': field,
                        '类别': category,
                        '持仓市值(万元)': round(position_value_wan, 2),
                        '集中度比例(%)': round(concentration_percentage, 2)
                    })

                    # 2.4 透视symbol_category字段输出列表新增name列
                    if field == 'symbol_category' and 'product_name' in data.columns:
                        # 获取该symbol_category对应的product_name
                        category_data = data[data[field] == category]
                        if not category_data.empty:
                            # 选择第一个product_name作为代表
                            product_name = category_data['product_name'].iloc[0]
                            concentration_data[-1]['产品名称'] = product_name

        return pd.DataFrame(concentration_data)
    
    def _industry_contribution_analysis(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        行业持仓市值贡献比例分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            行业贡献分析结果
        """
        contribution_results = {}
        
        # 所有持仓的行业贡献分析
        contribution_results['overall'] = self._calculate_industry_contribution(cta_data, "全部持仓")
        
        # 按策略类别分组的行业贡献分析
        if 'strategy_category' in cta_data.columns:
            strategy_categories = cta_data['strategy_category'].unique()
            
            for strategy in strategy_categories:
                strategy_data = cta_data[cta_data['strategy_category'] == strategy]
                contribution_results[f'strategy_{strategy}'] = self._calculate_industry_contribution(
                    strategy_data, f"策略_{strategy}"
                )
        
        return contribution_results
    
    def _calculate_industry_contribution(self, data: pd.DataFrame, category_name: str) -> pd.DataFrame:
        """
        计算行业贡献比例
        
        Args:
            data: 数据集
            category_name: 类别名称
            
        Returns:
            行业贡献分析结果DataFrame
        """
        if 'industry' not in data.columns:
            logger.warning("数据中缺少industry字段")
            return pd.DataFrame()
        
        # 按行业汇总持仓市值
        if 'position_amount' in data.columns:
            industry_positions = data.groupby('industry')['position_amount'].sum()
        else:
            # 使用profit_loss_amount作为代理
            industry_positions = data.groupby('industry')['profit_loss_amount'].sum()
        
        # 分离正负持仓
        positive_positions = industry_positions[industry_positions > 0]
        negative_positions = industry_positions[industry_positions < 0]
        
        total_positive = positive_positions.sum()
        total_negative = negative_positions.sum()
        
        contribution_data = []
        
        # 计算正持仓贡献比例
        for industry, position_value in positive_positions.items():
            if total_positive > 0:
                contribution_ratio = position_value / total_positive
                contribution_data.append({
                    '分析类别': category_name,
                    '行业': industry,
                    '持仓市值': position_value,
                    '持仓方向': '多头',
                    '贡献比例': contribution_ratio,
                    '贡献类型': '正贡献'
                })
        
        # 计算负持仓贡献比例
        for industry, position_value in negative_positions.items():
            if total_negative < 0:
                contribution_ratio = position_value / total_negative
                contribution_data.append({
                    '分析类别': category_name,
                    '行业': industry,
                    '持仓市值': position_value,
                    '持仓方向': '空头',
                    '贡献比例': contribution_ratio,
                    '贡献类型': '负贡献'
                })
        
        return pd.DataFrame(contribution_data)
    
    def _position_distribution_analysis(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        持仓分布统计分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            持仓分布统计结果
        """
        distribution_stats = {}
        
        # 基本统计
        if 'position_amount' in cta_data.columns:
            position_data = cta_data['position_amount']
        else:
            position_data = cta_data['profit_loss_amount'].abs()
        
        distribution_stats['总持仓规模'] = position_data.sum()
        distribution_stats['平均持仓规模'] = position_data.mean()
        distribution_stats['持仓规模标准差'] = position_data.std()
        distribution_stats['最大单笔持仓'] = position_data.max()
        distribution_stats['最小单笔持仓'] = position_data.min()
        
        # 按合约类别统计
        if 'symbol_category' in cta_data.columns:
            symbol_stats = cta_data.groupby('symbol_category')[position_data.name].agg([
                'sum', 'mean', 'count', 'std'
            ]).round(2)
            symbol_stats.columns = ['总持仓', '平均持仓', '持仓数量', '持仓标准差']
            distribution_stats['合约类别分布'] = symbol_stats
        
        # 按多空方向统计
        if 'position_direction' in cta_data.columns:
            direction_stats = cta_data.groupby('position_direction')[position_data.name].agg([
                'sum', 'mean', 'count'
            ]).round(2)
            direction_stats.columns = ['总持仓', '平均持仓', '持仓数量']
            distribution_stats['多空方向分布'] = direction_stats
        
        return distribution_stats
    
    def _risk_exposure_analysis(self, cta_data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        风险敞口分析
        
        Args:
            cta_data: CTA策略数据
            
        Returns:
            风险敞口分析结果
        """
        exposure_results = {}
        
        # 按行业计算风险敞口
        if 'industry' in cta_data.columns:
            industry_exposure = self._calculate_exposure_by_field(cta_data, 'industry')
            exposure_results['industry_exposure'] = industry_exposure
        
        # 按合约类别计算风险敞口
        if 'symbol_category' in cta_data.columns:
            symbol_exposure = self._calculate_exposure_by_field(cta_data, 'symbol_category')
            exposure_results['symbol_category_exposure'] = symbol_exposure
        
        # 按策略类别计算风险敞口
        if 'strategy_category' in cta_data.columns:
            strategy_exposure = self._calculate_exposure_by_field(cta_data, 'strategy_category')
            exposure_results['strategy_category_exposure'] = strategy_exposure
        
        return exposure_results
    
    def _calculate_exposure_by_field(self, data: pd.DataFrame, field: str) -> pd.DataFrame:
        """
        按指定字段计算风险敞口
        
        Args:
            data: 数据集
            field: 分组字段
            
        Returns:
            风险敞口分析结果
        """
        if 'position_amount' in data.columns:
            position_col = 'position_amount'
        else:
            position_col = 'profit_loss_amount'
        
        # 计算多头和空头敞口
        long_exposure = data[data['position_direction'] == 'Long'].groupby(field)[position_col].sum()
        short_exposure = data[data['position_direction'] == 'Short'].groupby(field)[position_col].sum()
        
        # 计算净敞口和总敞口
        net_exposure = long_exposure.add(short_exposure, fill_value=0)
        gross_exposure = long_exposure.abs().add(short_exposure.abs(), fill_value=0)
        
        exposure_df = pd.DataFrame({
            '多头敞口': long_exposure,
            '空头敞口': short_exposure,
            '净敞口': net_exposure,
            '总敞口': gross_exposure
        }).fillna(0)
        
        # 计算敞口比例
        total_gross_exposure = gross_exposure.sum()
        if total_gross_exposure > 0:
            exposure_df['敞口比例'] = gross_exposure / total_gross_exposure
        else:
            exposure_df['敞口比例'] = 0
        
        return exposure_df.round(2)
